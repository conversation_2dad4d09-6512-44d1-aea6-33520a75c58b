<?php

  use classes\HydrateTrait;
  use classes\LogTrait;
  use classes\TimesTrait;

  AppModel::loadModelClass('IndufastWorkdayLineModel');

  class IndufastWorkdayLine extends IndufastWorkdayLineModel {

    use ModelFillTrait;
    use ValidationTrait;
    use PropertyCastTrait;
    use TimesTrait;
    use LogTrait;
    use HydrateTrait;

    public IndufastWorkday $workday;

    public string $duration {
      get {
        $start = DateTime::createFromFormat('G:i:s', $this->start ?? '00:00:00');
        $end = DateTime::createFromFormat('G:i:s', $this->end ?? '00:00:00');

        if ($start && $end) {
          return $start->diff($end)->format('%H:%I:%S');
        }

        return '00:00:00';
      }
    }
    public bool $canBeVoid;
    public bool $canBeSetAsTravelToEnd;
    public bool $canBeSetAsTravelFromStart;
    public array $default_relations = ['setCanBeVoid', 'travelTimes'];
    public int $parent_id;

    const string TYPE_TRAVEL_TO = 'travel-to';
    const string TYPE_TRAVEL_FROM = 'travel-from';
    const string TYPE_HOURS = 'hours';
    const string TYPE_LEAVE = 'leave';
    const string TYPE_SPECIAL_LEAVE = 'special-leave';
    const string TYPE_SICK = 'sick';

    const array CAST_PROPERTIES = [
      'id'          => 'int',
      'workday_id'  => 'int',
      'external_id' => 'int',
      'distance'    => 'float',
      'void'        => 'boolean',
      'from_db'     => 'hidden',
      'insertTS'    => 'hidden',
      'workday'     => 'hidden',
      'updateTS'    => 'hidden',
    ];

    protected array $fillable = [
      'workday_id'  => 'required|integer|exists:indufast_workday,id|locked_month:{workday_id}',
      'remark'      => 'string|nullable',
    ];

    public function getFillable(): array {
      $this->fillable['type'] = 'nullable|in:' . implode(',', [
        self::TYPE_TRAVEL_TO,
        self::TYPE_TRAVEL_FROM,
        self::TYPE_HOURS,
        self::TYPE_LEAVE,
        self::TYPE_SPECIAL_LEAVE,
        self::TYPE_SICK,
      ]);

      $this->fillable['start'] = [
        'required',
        'callback' => fn($v) => IndufastWorkdayLine::valid_sql_time($v),
        'range:end,asc',
      ];
      $this->fillable['end'] = [
        'required',
        'callback' => fn($v) => IndufastWorkdayLine::valid_sql_time($v),
        'range:start,desc',
      ];

      return $this->fillable;
    }

    /**
     * Override the validateProperty method to handle custom validation for time fields.
     */
    public function validateProperty(string $property): bool {
      $value = $this->{$property};

      return match ($property) {
        'start', 'end' => self::valid_sql_time($value),
        default => parent::validateProperty($property),
      };
    }

    public function parent_name(): string {
      return IndufastWorkday::class;
    }

    public function parent_id(): int {
      return $this->workday_id;
    }

    protected function workday(): IndufastWorkday {
      return $this->workday ?? $this->workday = IndufastWorkday::find_by_id($this->workday_id);
    }

    /**
     * @throws Exception
     */
    public function setWorkday(IndufastWorkday $workday): void {
      if ($workday->id != $this->workday_id) {
        throw new Exception("Workday ID mismatch: $workday->id != $this->workday_id");
      }

      $this->workday = $workday;
    }

    public function setDefaults(): void {
      parent::setDefaults();
      $this->void = 0;
      $this->insertTS = date("Y-m-d H:i:s");
    }

    public function travelTimes(): void {
      $workday = $this->workday()->load(['lines']);

      $lineIds = array_map('intval', array_column($workday->lines, 'id'));
      $lineIndex = array_search($this->id, $lineIds);

      $this->canBeSetAsTravelToEnd = !$this->void && $this->type != 'travel-from' && $lineIndex != count($workday->lines) - 1;
      $this->canBeSetAsTravelFromStart = !$this->void && $this->type != 'travel-to' && $lineIndex != 0;
    }

    public function setCanBeVoid(): void {
      $workday = $this->workday();

      if (empty($this->external_id) && $this->id != $workday->travel_to_end_line_id && $this->id != $workday->travel_from_start_line_id) {
        $this->canBeVoid = true;
        return;
      }

      $voidableTravelToId = null;
      foreach ($workday->lines() as $line) {
        if (($this->type == 'travel-to' || $line->void) && $line->id != $workday->travel_to_end_line_id) {
          $voidableTravelToId = $line->id;
        }

        if ($line->id == $workday->travel_to_end_line_id || !$line->void) {
          break;
        }
      }

      $voidableTravelFromId = null;
      foreach (array_reverse($workday->lines()) as $line) {
        if (($this->type == 'travel-from' || $line->void) && $line->id != $workday->travel_from_start_line_id) {
          $voidableTravelFromId = $line->id;
        }

        if ($line->id == $workday->travel_to_end_line_id || !$line->void) {
          break;
        }
      }

      $this->canBeVoid = ($this->id == $voidableTravelToId || $this->id == $voidableTravelFromId);
    }
 }